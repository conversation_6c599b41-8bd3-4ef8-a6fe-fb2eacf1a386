android.suppressUnsupportedCompileSdk=35

# Project-wide Gradle settings.
org.gradle.daemon=true
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx512m -XX:MaxMetaspaceSize=256m
org.gradle.jvmargs=-Xmx2048m -XX:MaxMetaspaceSize=512m

# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true

# Version of flipper SDK to use with React Native
FLIPPER_VERSION=0.182.0

# Use this property to specify which architecture you want to build.
# You can also override it from the CLI using
# ./gradlew <task> -PreactNativeArchitectures=x86_64
reactNativeArchitectures=armeabi-v7a,arm64-v8a,x86,x86_64

# Use this property to enable support to the new architecture.
# This will allow you to use TurboModules and the Fabric render in
# your application. You should enable this flag either if you want
# to write custom TurboModules/Fabric components OR use libraries that
# are providing them.
newArchEnabled=false

# Use this property to enable or disable the Hermes JS engine.
# If set to false, you will be using JSC instead.
hermesEnabled=true

# Fix for Android SDK 35 compatibility
android.enableDexingArtifactTransform.desugaring=false
android.nonTransitiveRClass=false
android.nonFinalResIds=false

; MYAPP_UPLOAD_STORE_FILE=my-upload-key.keystore
; MYAPP_UPLOAD_KEY_ALIAS=my-key-alias
; MYAPP_UPLOAD_STORE_PASSWORD=zaq1@WSX
; MYAPP_UPLOAD_KEY_PASSWORD=zaq1@WSX

MYAPP_UPLOAD_STORE_FILE=homeprofit-key.keystore
MYAPP_UPLOAD_KEY_ALIAS=homeprofit-key
MYAPP_UPLOAD_STORE_PASSWORD=Yg2h88mU
MYAPP_UPLOAD_KEY_PASSWORD=Yg2h88mU


; key.store=debug.keystore
; key.alias=androiddebugkey
; key.store.password=android
; key.alias.password=android
